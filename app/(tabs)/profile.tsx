import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, Image, ScrollView, TouchableOpacity, Switch, Alert, TextInput, Platform } from 'react-native';
import { Stack, router } from 'expo-router';
import { Settings, LogOut, Award, Heart, Camera, Leaf, FileText, Shield, Lock, Eye, EyeOff, Save, Edit3, X } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { Colors } from '@/constants/colors';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useGarden } from '@/hooks/useGardenStore';
import { useFavorites } from '@/hooks/useFavoritesStore';
import { useAuth } from '@/hooks/useAuth';
import { DatabaseService, UserProfile } from '@/services/database';
import { supabase } from '@/lib/supabase';
import { StorageService } from '@/services/r2Storage';

export default function ProfileScreen() {
  const { plants } = useGarden();
  const { favorites } = useFavorites();
  const { user, signOut } = useAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isPublicProfile, setIsPublicProfile] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Editable profile fields
  const [displayName, setDisplayName] = useState('');
  const [bio, setBio] = useState('');
  const [location, setLocation] = useState('');
  const [website, setWebsite] = useState('');
  const [avatarUri, setAvatarUri] = useState<string | null>(null);

  // Load user profile data
  useEffect(() => {
    const loadUserProfile = async () => {
      if (!user) return;

      try {
        let profile = await DatabaseService.getUserProfile(user.id);

        // If profile doesn't exist, create one
        if (!profile) {
          const newProfile = {
            user_id: user.id,
            username: user.user_metadata?.preferred_username || null,
            display_name: user.user_metadata?.full_name || 'Plant Lover',
            bio: undefined,
            avatar_url: user.user_metadata?.avatar_url || null,
            location: undefined,
            website_url: undefined,
            is_public: false,
            allow_garden_sharing: false,
            allow_profile_indexing: false,
            experience_level: 'beginner' as const,
            total_identifications: 0,
            total_diagnoses: 0,
            community_points: 0,
            achievements: [],
          };

          profile = await DatabaseService.createUserProfile(newProfile);
        } else {
          // Update profile stats to ensure they're current
          profile = await DatabaseService.updateUserProfileStats(user.id) || profile;
        }

        if (profile) {
          setUserProfile(profile);
          setIsPublicProfile(profile.is_public);
          // Initialize editable fields
          setDisplayName(profile.display_name || '');
          setBio(profile.bio || '');
          setLocation(profile.location || '');
          setWebsite(profile.website_url || '');
          setAvatarUri(profile.avatar_url || null);
        }
      } catch (error) {
        console.error('Error loading user profile:', error);
      }
    };

    loadUserProfile();
  }, [user]);

  // Refresh profile stats when the screen is focused
  useEffect(() => {
    const refreshProfileStats = async () => {
      if (user && userProfile) {
        try {
          const updatedProfile = await DatabaseService.updateUserProfileStats(user.id);
          if (updatedProfile) {
            setUserProfile(updatedProfile);
          }
        } catch (error) {
          console.error('Error refreshing profile stats:', error);
        }
      }
    };

    // Set up an interval to refresh stats periodically
    const interval = setInterval(refreshProfileStats, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [user, userProfile]);

  // User data from auth and profile
  const userData = {
    name: displayName || user?.user_metadata?.full_name || userProfile?.display_name || 'Plant Lover',
    email: user?.email || '<EMAIL>',
    avatar: avatarUri === null
      ? 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=776&q=80'
      : avatarUri || user?.user_metadata?.avatar_url || userProfile?.avatar_url || 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=776&q=80',
    joinDate: user?.created_at ? new Date(user.created_at) : new Date(),
    identifications: userProfile?.total_identifications || 0,
    diagnoses: userProfile?.total_diagnoses || 0,
    communityPoints: userProfile?.community_points || 0,
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handlePublicProfileToggle = async (value: boolean) => {
    if (!user || !userProfile) return;

    try {
      const updatedProfile = await DatabaseService.updateUserProfile(user.id, {
        is_public: value,
      });

      if (updatedProfile) {
        setUserProfile(updatedProfile);
        setIsPublicProfile(value);
      }
    } catch (error) {
      console.error('Error updating profile visibility:', error);
      Alert.alert('Error', 'Failed to update profile visibility');
    }
  };

  const handleEditToggle = () => {
    if (isEditing) {
      // Reset fields to original values when canceling edit
      if (userProfile) {
        setDisplayName(userProfile.display_name || '');
        setBio(userProfile.bio || '');
        setLocation(userProfile.location || '');
        setWebsite(userProfile.website_url || '');
        setAvatarUri(userProfile.avatar_url || null);
      }
    }
    setIsEditing(!isEditing);
  };

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Sorry, we need camera roll permissions to change your profile picture.');
        return;
      }

      // Handle platform differences for mediaTypes
      const mediaTypes = Platform.OS === 'web'
        ? 'images' as any  // Web uses string format
        : ImagePicker.MediaTypeOptions?.Images || 'images' as any; // Native uses enum or fallback to string

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setAvatarUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      Alert.alert('Error', `Failed to pick image: ${errorMessage}. Please try again.`);
    }
  };

  const uploadAvatar = async (uri: string): Promise<string | null> => {
    try {
      console.log('Starting avatar upload for URI:', uri);

      const fileExt = uri.split('.').pop() || 'jpg';
      const fileName = `${user?.id}-${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      console.log('Upload details:', { fileName, filePath, fileExt });

      // The new uploadImageFromUri method handles compression and blob conversion

      console.log('Uploading avatar image with compression...');

      // Use the new storage service with compression for avatars
      const uploadResult = await StorageService.uploadImageFromUri(
        uri,
        filePath
      );

      if (!uploadResult.success) {
        throw new Error(`Upload failed: ${uploadResult.error}`);
      }

      console.log('Upload successful:', uploadResult.url);
      return uploadResult.url || null;
    } catch (error) {
      console.error('Error uploading avatar:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to upload avatar image');
    }
  };

  const removeAvatar = () => {
    setAvatarUri(null);
  };

  // Debug function to test storage
  const testStorage = async () => {
    try {
      console.log('Testing storage health...');
      const health = await StorageService.checkStorageHealth();
      console.log('Storage health result:', health);

      Alert.alert(
        'Storage Health Check',
        `R2: ${health.r2Available ? '✅ Available' : '❌ Not Available'}\n` +
        `Supabase: ${health.supabaseAvailable ? '✅ Available' : '❌ Not Available'}\n` +
        `Recommended: ${health.recommendedMethod.toUpperCase()}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Storage test error:', error);
      Alert.alert('Storage Test Failed', error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const handleSaveProfile = async () => {
    if (!user || !userProfile) {
      Alert.alert('Error', 'Unable to save profile. Please try again.');
      return;
    }

    setIsSaving(true);

    try {
      let avatarUrl = userProfile.avatar_url;

      // Handle avatar changes
      if (avatarUri === null) {
        // User removed the avatar
        avatarUrl = null;
      } else if (avatarUri && avatarUri !== userProfile.avatar_url) {
        // User selected a new avatar
        try {
          const uploadedUrl = await uploadAvatar(avatarUri);
          if (uploadedUrl) {
            avatarUrl = uploadedUrl;
          }
        } catch (uploadError) {
          console.error('Avatar upload failed:', uploadError);
          const errorMessage = uploadError instanceof Error ? uploadError.message : 'Unknown upload error';

          // For now, let's try to save as data URL as a fallback
          try {
            // Convert image to data URL for temporary storage
            const response = await fetch(avatarUri);
            const blob = await response.blob();

            // Check if it's a reasonable size for data URL (limit to 1MB for data URLs)
            if (blob.size <= 1024 * 1024) {
              const reader = new FileReader();
              const dataUrl = await new Promise<string>((resolve, reject) => {
                reader.onload = () => resolve(reader.result as string);
                reader.onerror = reject;
                reader.readAsDataURL(blob);
              });

              avatarUrl = dataUrl;
              console.log('Using data URL as fallback for avatar');
            } else {
              throw new Error('Image too large for fallback storage');
            }
          } catch (fallbackError) {
            console.error('Fallback storage also failed:', fallbackError);

            // Show error and ask user if they want to continue without avatar update
            const shouldContinue = await new Promise<boolean>((resolve) => {
              Alert.alert(
                'Avatar Upload Failed',
                `${errorMessage}\n\nStorage upload failed and image is too large for temporary storage. Would you like to save your other profile changes without updating the avatar?`,
                [
                  { text: 'Cancel', style: 'cancel', onPress: () => resolve(false) },
                  { text: 'Save Without Avatar', onPress: () => resolve(true) }
                ]
              );
            });

            if (!shouldContinue) {
              setIsSaving(false);
              return;
            }

            // Reset avatar to original value since upload failed
            avatarUrl = userProfile.avatar_url;
          }
        }
      }

      // Update profile
      const updateData = {
        display_name: displayName.trim(),
        bio: bio.trim() || undefined,
        location: location.trim() || undefined,
        website_url: website.trim() || undefined,
        avatar_url: avatarUrl || undefined,
      };

      const updatedProfile = await DatabaseService.updateUserProfile(user.id, updateData);

      if (updatedProfile) {
        setUserProfile(updatedProfile);
        setIsEditing(false);
        Alert.alert('Success', 'Your profile has been updated successfully!');
      } else {
        Alert.alert('Error', 'Failed to update profile. Please try again.');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert('Error', 'Failed to save profile. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetPassword = async () => {
    if (!user?.email) {
      Alert.alert('Error', 'Unable to find your email address.');
      return;
    }

    Alert.alert(
      'Reset Password',
      'A password reset link will be sent to your email address.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send Reset Link',
          onPress: async () => {
            try {
              const { error } = await supabase.auth.resetPasswordForEmail(user.email!, {
                redirectTo: 'https://plantidguide.com/reset-password',
              });

              if (error) {
                Alert.alert('Error', 'Failed to send password reset email. Please try again.');
              } else {
                Alert.alert('Success', 'Password reset link sent to your email address.');
              }
            } catch (error) {
              console.error('Error sending password reset:', error);
              Alert.alert('Error', 'Failed to send password reset email. Please try again.');
            }
          },
        },
      ]
    );
  };
  
  return (
    <ScrollView style={styles.container} testID="profile-screen">
      <Stack.Screen 
        options={{ 
          title: 'Profile',
          headerRight: () => (
            <TouchableOpacity style={styles.headerButton}>
              <Settings size={24} color={Colors.text} />
            </TouchableOpacity>
          ),
        }} 
      />
      
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Image source={{ uri: userData.avatar }} style={styles.avatar} />
          {isEditing && (
            <>
              <TouchableOpacity onPress={pickImage} style={styles.cameraButton}>
                <Camera size={20} color={Colors.background} />
              </TouchableOpacity>
              {avatarUri && (
                <TouchableOpacity onPress={removeAvatar} style={styles.removeButton}>
                  <X size={16} color={Colors.background} />
                </TouchableOpacity>
              )}
            </>
          )}
        </View>

        {isEditing ? (
          <TextInput
            style={styles.nameInput}
            value={displayName}
            onChangeText={setDisplayName}
            placeholder="Display Name"
            placeholderTextColor={Colors.textMuted}
          />
        ) : (
          <Text style={styles.name}>{userData.name}</Text>
        )}

        <Text style={styles.email}>{userData.email}</Text>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{plants.length}</Text>
            <Text style={styles.statLabel}>Garden</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{userData.identifications}</Text>
            <Text style={styles.statLabel}>Identified</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{userData.diagnoses}</Text>
            <Text style={styles.statLabel}>Diagnosed</Text>
          </View>
        </View>
      </View>

      {/* Profile Information Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Profile Information</Text>
          <TouchableOpacity
            onPress={isEditing ? handleSaveProfile : handleEditToggle}
            style={styles.editButton}
            disabled={isSaving}
          >
            {isSaving ? (
              <Text style={styles.editButtonText}>Saving...</Text>
            ) : isEditing ? (
              <>
                <Save size={16} color={Colors.primary} />
                <Text style={styles.editButtonText}>Save</Text>
              </>
            ) : (
              <>
                <Edit3 size={16} color={Colors.primary} />
                <Text style={styles.editButtonText}>Edit</Text>
              </>
            )}
          </TouchableOpacity>
        </View>

        <Card style={styles.profileCard}>
          <View style={styles.profileField}>
            <Text style={styles.fieldLabel}>Display Name</Text>
            {isEditing ? (
              <TextInput
                style={styles.fieldInput}
                value={displayName}
                onChangeText={setDisplayName}
                placeholder="Your display name"
                placeholderTextColor={Colors.textMuted}
              />
            ) : (
              <Text style={styles.fieldValue}>{displayName || 'No display name set'}</Text>
            )}
          </View>

          <View style={styles.fieldDivider} />

          <View style={styles.profileField}>
            <Text style={styles.fieldLabel}>Bio</Text>
            {isEditing ? (
              <TextInput
                style={styles.bioInput}
                value={bio}
                onChangeText={setBio}
                placeholder="Tell us about yourself..."
                placeholderTextColor={Colors.textMuted}
                multiline
                numberOfLines={3}
              />
            ) : (
              <Text style={styles.fieldValue}>{bio || 'No bio added yet'}</Text>
            )}
          </View>

          <View style={styles.fieldDivider} />

          <View style={styles.profileField}>
            <Text style={styles.fieldLabel}>Location</Text>
            {isEditing ? (
              <TextInput
                style={styles.fieldInput}
                value={location}
                onChangeText={setLocation}
                placeholder="Your location"
                placeholderTextColor={Colors.textMuted}
              />
            ) : (
              <Text style={styles.fieldValue}>{location || 'No location added'}</Text>
            )}
          </View>

          <View style={styles.fieldDivider} />

          <View style={styles.profileField}>
            <Text style={styles.fieldLabel}>Website</Text>
            {isEditing ? (
              <TextInput
                style={styles.fieldInput}
                value={website}
                onChangeText={setWebsite}
                placeholder="Your website URL"
                placeholderTextColor={Colors.textMuted}
                autoCapitalize="none"
                keyboardType="url"
              />
            ) : (
              <Text style={styles.fieldValue}>{website || 'No website added'}</Text>
            )}
          </View>

          {isEditing && (
            <>
              <View style={styles.fieldDivider} />
              <TouchableOpacity onPress={handleEditToggle} style={styles.cancelButton}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            </>
          )}
        </Card>
      </View>



      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Achievements</Text>
        <Card>
          <View style={styles.achievementGrid}>
            {/* Plant Novice Achievement */}
            <View style={styles.achievementItem}>
              <View style={[
                styles.achievementBadge,
                { backgroundColor: userData.identifications >= 10 ? Colors.success : Colors.textMuted }
              ]}>
                <Camera size={20} color={Colors.background} />
              </View>
              <Text style={[
                styles.achievementTitle,
                userData.identifications < 10 && styles.lockedText
              ]}>
                Plant Novice
              </Text>
              <Text style={[
                styles.achievementDesc,
                userData.identifications < 10 && styles.lockedText
              ]}>
                Identify 10+ plants
              </Text>
            </View>

            {/* Plant Expert Achievement */}
            <View style={styles.achievementItem}>
              <View style={[
                styles.achievementBadge,
                { backgroundColor: userData.identifications >= 100 ? Colors.primary : Colors.textMuted }
              ]}>
                <Award size={20} color={Colors.background} />
              </View>
              <Text style={[
                styles.achievementTitle,
                userData.identifications < 100 && styles.lockedText
              ]}>
                Plant Expert
              </Text>
              <Text style={[
                styles.achievementDesc,
                userData.identifications < 100 && styles.lockedText
              ]}>
                Identify 100+ plants
              </Text>
            </View>

            {/* Plant Guru Achievement */}
            <View style={styles.achievementItem}>
              <View style={[
                styles.achievementBadge,
                { backgroundColor: userData.identifications >= 300 ? Colors.warning : Colors.textMuted }
              ]}>
                <Leaf size={20} color={Colors.background} />
              </View>
              <Text style={[
                styles.achievementTitle,
                userData.identifications < 300 && styles.lockedText
              ]}>
                Plant Guru
              </Text>
              <Text style={[
                styles.achievementDesc,
                userData.identifications < 300 && styles.lockedText
              ]}>
                Identify 300+ plants
              </Text>
            </View>
          </View>
        </Card>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account</Text>
        <Card style={styles.accountCard}>
          <TouchableOpacity style={styles.accountOption} onPress={handleResetPassword}>
            <Lock size={20} color={Colors.primary} style={styles.accountIcon} />
            <Text style={styles.accountOptionText}>Reset Password</Text>
          </TouchableOpacity>

          <View style={styles.accountDivider} />

          <View style={styles.accountOption}>
            <View style={styles.accountLeft}>
              {isPublicProfile ? (
                <Eye size={20} color={Colors.primary} style={styles.accountIcon} />
              ) : (
                <EyeOff size={20} color={Colors.primary} style={styles.accountIcon} />
              )}
              <Text style={styles.accountOptionText}>Public Profile</Text>
            </View>
            <Switch
              value={isPublicProfile}
              onValueChange={handlePublicProfileToggle}
              trackColor={{ false: Colors.border, true: Colors.primary }}
              thumbColor={Colors.background}
            />
          </View>

          <View style={styles.accountDivider} />

          <TouchableOpacity
            style={styles.accountOption}
            onPress={() => router.push('/legal/terms')}
          >
            <FileText size={20} color={Colors.primary} style={styles.accountIcon} />
            <Text style={styles.accountOptionText}>Terms of Service</Text>
          </TouchableOpacity>

          <View style={styles.accountDivider} />

          <TouchableOpacity
            style={styles.accountOption}
            onPress={() => router.push('/legal/privacy')}
          >
            <Shield size={20} color={Colors.primary} style={styles.accountIcon} />
            <Text style={styles.accountOptionText}>Privacy Policy</Text>
          </TouchableOpacity>

          <View style={styles.accountDivider} />

          <TouchableOpacity style={styles.accountOption} onPress={testStorage}>
            <Shield size={20} color={Colors.primary} style={styles.accountIcon} />
            <Text style={styles.accountOptionText}>Test Storage</Text>
          </TouchableOpacity>

          <View style={styles.accountDivider} />

          <TouchableOpacity style={styles.accountOption} onPress={handleSignOut}>
            <LogOut size={20} color={Colors.error} style={styles.accountIcon} />
            <Text style={[styles.accountOptionText, { color: Colors.error }]}>Sign Out</Text>
          </TouchableOpacity>
        </Card>
      </View>
      
      <View style={styles.footer}>
        <Text style={styles.footerText}>PlantIDGuide v1.0.0</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  headerButton: {
    padding: 8,
    marginRight: 8,
  },
  header: {
    alignItems: 'center',
    padding: 20,
    paddingBottom: 0,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    color: Colors.textLight,
    marginBottom: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
  },
  statLabel: {
    fontSize: 14,
    color: Colors.textLight,
  },
  statDivider: {
    width: 1,
    height: '100%',
    backgroundColor: Colors.border,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  lockedText: {
    color: Colors.textMuted,
  },
  accountCard: {
    padding: 0,
  },
  settingsCard: {
    padding: 0,
  },
  accountOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  accountIcon: {
    marginRight: 16,
  },
  accountOptionText: {
    fontSize: 16,
    color: Colors.text,
  },
  accountDivider: {
    height: 1,
    backgroundColor: Colors.border,
    width: '100%',
  },
  accountLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    fontSize: 16,
    color: Colors.text,
    marginLeft: 12,
  },
  settingDivider: {
    height: 1,
    backgroundColor: Colors.border,
    marginLeft: 32,
  },
  achievementGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  achievementItem: {
    flex: 1,
    minWidth: '30%',
    alignItems: 'center',
    padding: 12,
  },
  achievementBadge: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  achievementTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 4,
  },
  achievementDesc: {
    fontSize: 10,
    color: Colors.textMuted,
    textAlign: 'center',
    lineHeight: 14,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: Colors.textMuted,
  },
  // New styles for editable profile
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: Colors.background,
  },
  removeButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: Colors.error,
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  nameInput: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
    textAlign: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    paddingVertical: 8,
    paddingHorizontal: 16,
    minWidth: 200,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    backgroundColor: Colors.cardBackground,
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  editButtonText: {
    fontSize: 14,
    color: Colors.primary,
    marginLeft: 4,
    fontWeight: '600',
  },
  profileCard: {
    padding: 16,
  },
  profileField: {
    marginVertical: 8,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  fieldValue: {
    fontSize: 16,
    color: Colors.textLight,
    lineHeight: 22,
  },
  fieldInput: {
    fontSize: 16,
    color: Colors.text,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: Colors.background,
  },
  bioInput: {
    fontSize: 16,
    color: Colors.text,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: Colors.background,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  fieldDivider: {
    height: 1,
    backgroundColor: Colors.border,
    marginVertical: 8,
  },
  cancelButton: {
    alignSelf: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    backgroundColor: Colors.border,
    marginTop: 8,
  },
  cancelButtonText: {
    fontSize: 14,
    color: Colors.textMuted,
    fontWeight: '600',
  },
});