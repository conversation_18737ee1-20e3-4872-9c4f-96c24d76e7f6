# Cloudflare R2 Storage Implementation

This document describes the Cloudflare R2 storage implementation for the PlantConnects app.

## Overview

The app now supports Cloudflare R2 as the primary storage solution for images (plant scans, diagnoses, and avatars), with automatic fallback to Supabase storage and data URLs.

## Features

- ✅ **Automatic Image Compression**: Images are compressed before upload to optimize storage and bandwidth
- ✅ **Multi-tier Fallback**: R2 → Supabase → Data URL fallback system
- ✅ **S3-Compatible API**: Uses AWS SDK for reliable R2 integration
- ✅ **Automatic Path Generation**: Unique file paths with user ID and timestamp
- ✅ **Health Monitoring**: Built-in storage health checks

## Configuration

### Environment Variables

The following environment variables must be set in your `.env.local` file:

```env
# Cloudflare R2 Storage Configuration
CLOUDFLARE_ACCOUNT_ID=your_account_id
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
R2_BUCKET_NAME=your_bucket_name
R2_ENDPOINT_URL=https://your_account_id.r2.cloudflarestorage.com
```

### Cloudflare R2 Bucket Setup

1. **Create R2 Bucket**:
   - Log into Cloudflare Dashboard
   - Go to R2 Object Storage
   - Create a new bucket (e.g., `expo-plantsconnect-live-040825`)

2. **Configure CORS** (Required for direct uploads):
   ```json
   [
     {
       "AllowedOrigins": ["*"],
       "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
       "AllowedHeaders": ["*"],
       "ExposeHeaders": ["ETag"],
       "MaxAgeSeconds": 3000
     }
   ]
   ```

3. **Create API Token**:
   - Go to R2 → Manage R2 API tokens
   - Create token with "Object Read & Write" permissions
   - Note down the Access Key ID and Secret Access Key

4. **Configure Public Access** (Optional):
   - Enable public access for the bucket if you want direct public URLs
   - Configure custom domain if needed

## Usage

### Basic Image Upload

```typescript
import { StorageService } from '@/services/r2Storage';

// Upload with automatic compression
const result = await StorageService.uploadImageFromUri(
  imageUri,
  'scans/user-123-timestamp-abc.jpg'
);

if (result.success) {
  console.log('Image uploaded:', result.url);
} else {
  console.error('Upload failed:', result.error);
}
```

### Manual Blob Upload

```typescript
// Upload blob directly
const result = await StorageService.uploadImage(
  blob,
  'avatars/user-123-timestamp-xyz.jpg',
  'image/jpeg'
);
```

### Image Compression

```typescript
import { compressForStorage, compressForLLM } from '@/utils/imageCompression';

// Compress for storage (1024x1024, 80% quality)
const storageResult = await compressForStorage(imageUri);

// Compress for LLM processing (512x512, 70% quality)
const llmResult = await compressForLLM(imageUri);
```

## Storage Hierarchy

The system uses a three-tier fallback approach:

1. **Primary: Cloudflare R2**
   - Fast, cost-effective object storage
   - Global CDN distribution
   - S3-compatible API

2. **Secondary: Supabase Storage**
   - Integrated with existing auth system
   - Reliable fallback option
   - Built-in public URL generation

3. **Tertiary: Data URLs**
   - For small images only (< 2MB)
   - Stored directly in database
   - No external dependencies

## File Organization

Images are organized in the following structure:

```
bucket/
├── avatars/
│   └── user-{userId}-{timestamp}-{random}.jpg
├── scans/
│   └── user-{userId}-{timestamp}-{random}.jpg
└── diagnoses/
    └── user-{userId}-{timestamp}-{random}.jpg
```

## Testing

Run the R2 storage test to verify configuration:

```bash
# Test R2 connectivity and functionality
npx ts-node scripts/testR2Storage.ts
```

## Monitoring

Check storage health programmatically:

```typescript
const health = await StorageService.checkStorageHealth();
console.log('Storage status:', health);
// Output: { r2Available: true, supabaseAvailable: true, recommendedMethod: 'r2' }
```

## Troubleshooting

### Common Issues

1. **CORS Errors**:
   - Ensure CORS is properly configured on your R2 bucket
   - Check that all required headers are allowed

2. **Authentication Errors**:
   - Verify API credentials are correct
   - Ensure the API token has proper permissions

3. **Upload Failures**:
   - Check bucket name and endpoint URL
   - Verify network connectivity
   - Review console logs for detailed error messages

### Debug Mode

Enable detailed logging:

```typescript
// Enable storage uploads (they're enabled by default)
StorageService.enableStorageUploads(true);

// Check if uploads are enabled
const isEnabled = StorageService.isStorageUploadEnabled();
```

## Security Considerations

- **API Keys**: Never expose R2 credentials in client-side code
- **CORS**: Configure CORS restrictively for production
- **Public Access**: Consider using signed URLs for sensitive content
- **Rate Limiting**: Implement upload rate limiting to prevent abuse

## Performance Optimization

- **Image Compression**: Automatic compression reduces storage costs and transfer time
- **CDN**: R2 provides global CDN distribution for fast access
- **Fallback Strategy**: Multiple storage options ensure high availability

## Cost Optimization

- **Compression**: Reduces storage usage by 60-80%
- **Lifecycle Policies**: Consider implementing automatic cleanup for old files
- **Monitoring**: Track usage to optimize storage costs

## Migration

If migrating from another storage solution:

1. Update environment variables
2. Test with the provided test script
3. Gradually migrate existing images
4. Monitor for any issues during transition

## Support

For issues related to:
- **R2 Configuration**: Check Cloudflare R2 documentation
- **AWS SDK**: Refer to AWS SDK for JavaScript documentation
- **App Integration**: Review the StorageService implementation
