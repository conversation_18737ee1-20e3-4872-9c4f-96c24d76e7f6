import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import {
  ChevronDown,
  ChevronUp,
  AlertTriangle,
  Activity,
  Clock,
  TrendingUp,
  CheckCircle,
  XCircle,
  AlertCircle,
  Zap,
  Heart,
  Shield,
  Target,
  List,
  Lightbulb,
} from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { Card } from '@/components/ui/Card';
import { DiagnosisData } from '@/services/openrouter';

interface DiagnosisInfoCardProps {
  diagnosisData: DiagnosisData | {
    id: string;
    diagnosedProblem?: string;
    severity?: string;
    immediateActions?: string[];
    longTermCare?: string[];
    prognosis?: string;
    createdAt: Date;
  };
}

export const DiagnosisInfoCard: React.FC<DiagnosisInfoCardProps> = ({
  diagnosisData,
}) => {
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({
    immediateActions: true, // Keep immediate actions expanded by default
  });

  const toggleSection = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }));
  };

  const getSeverityColor = (severity?: string) => {
    switch (severity?.toLowerCase()) {
      case 'mild':
        return Colors.success;
      case 'moderate':
        return Colors.warning;
      case 'severe':
        return Colors.error;
      case 'critical':
        return Colors.error;
      default:
        return Colors.textMuted;
    }
  };

  const getSeverityIcon = (severity?: string) => {
    switch (severity?.toLowerCase()) {
      case 'mild':
        return CheckCircle;
      case 'moderate':
        return AlertCircle;
      case 'severe':
        return XCircle;
      case 'critical':
        return AlertTriangle;
      default:
        return AlertCircle;
    }
  };

  const renderDiagnosedProblem = () => {
    if (!diagnosisData.diagnosedProblem) return null;

    const SeverityIcon = getSeverityIcon(diagnosisData.severity);
    const severityColor = getSeverityColor(diagnosisData.severity);

    return (
      <Card style={[styles.problemCard, { borderLeftColor: severityColor }]}>
        <View style={styles.problemHeader}>
          <View style={styles.problemTitleContainer}>
            <AlertTriangle size={24} color={severityColor} />
            <Text style={styles.problemTitle}>Diagnosed Problem</Text>
          </View>
          {diagnosisData.severity && (
            <View style={[styles.severityBadge, { backgroundColor: severityColor }]}>
              <SeverityIcon size={16} color={Colors.background} />
              <Text style={styles.severityText}>
                {diagnosisData.severity.charAt(0).toUpperCase() + diagnosisData.severity.slice(1)}
              </Text>
            </View>
          )}
        </View>
        <Text style={styles.problemDescription}>{diagnosisData.diagnosedProblem}</Text>

        {'symptomsObserved' in diagnosisData && diagnosisData.symptomsObserved && (
          <View style={styles.symptomsContainer}>
            <Text style={styles.symptomsLabel}>Symptoms Observed:</Text>
            <Text style={styles.symptomsText}>{diagnosisData.symptomsObserved}</Text>
          </View>
        )}
      </Card>
    );
  };

  const renderActionsList = (actions: string[], title: string, icon: React.ComponentType<any>) => {
    if (!actions || actions.length === 0) return null;

    return (
      <View>
        {actions.map((action, index) => (
          <View key={index} style={styles.actionItem}>
            <View style={styles.actionNumber}>
              <Text style={styles.actionNumberText}>{index + 1}</Text>
            </View>
            <Text style={styles.actionText}>{action}</Text>
          </View>
        ))}
      </View>
    );
  };

  const renderExpandableSection = (
    title: string,
    content: React.ReactNode,
    sectionKey: string,
    icon: React.ComponentType<any>,
    defaultExpanded: boolean = false
  ) => {
    const isExpanded = expandedSections[sectionKey] ?? defaultExpanded;
    const IconComponent = icon;

    return (
      <Card style={styles.sectionCard}>
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => toggleSection(sectionKey)}
        >
          <View style={styles.sectionTitleContainer}>
            <IconComponent size={20} color={Colors.primary} />
            <Text style={styles.sectionTitle}>{title}</Text>
          </View>
          {isExpanded ? (
            <ChevronUp size={20} color={Colors.textMuted} />
          ) : (
            <ChevronDown size={20} color={Colors.textMuted} />
          )}
        </TouchableOpacity>
        {isExpanded && <View style={styles.sectionContent}>{content}</View>}
      </Card>
    );
  };

  const renderImmediateActions = () => {
    if (!diagnosisData.immediateActions || diagnosisData.immediateActions.length === 0) return null;

    return renderExpandableSection(
      'Immediate Actions',
      renderActionsList(diagnosisData.immediateActions, 'Immediate Actions', Zap),
      'immediateActions',
      Zap,
      true
    );
  };

  const renderLongTermCare = () => {
    if (!diagnosisData.longTermCare || diagnosisData.longTermCare.length === 0) return null;

    return renderExpandableSection(
      'Long-term Care',
      renderActionsList(diagnosisData.longTermCare, 'Long-term Care', Heart),
      'longTermCare',
      Heart
    );
  };

  const renderPreventionTips = () => {
    const preventionTips = 'preventionTips' in diagnosisData ? diagnosisData.preventionTips : undefined;
    if (!preventionTips || preventionTips.length === 0) return null;

    return renderExpandableSection(
      'Prevention Tips',
      renderActionsList(preventionTips, 'Prevention Tips', Shield),
      'preventionTips',
      Shield
    );
  };

  const renderProductRecommendations = () => {
    const productRecommendations = 'productRecommendations' in diagnosisData ? diagnosisData.productRecommendations : undefined;
    if (!productRecommendations || productRecommendations.length === 0) return null;

    return renderExpandableSection(
      'Product Recommendations',
      renderActionsList(productRecommendations, 'Product Recommendations', Target),
      'productRecommendations',
      Target
    );
  };

  const renderStepByStepInstructions = () => {
    const stepByStepInstructions = 'stepByStepInstructions' in diagnosisData ? diagnosisData.stepByStepInstructions : undefined;
    if (!stepByStepInstructions || stepByStepInstructions.length === 0) return null;

    return renderExpandableSection(
      'Step-by-Step Instructions',
      renderActionsList(stepByStepInstructions, 'Step-by-Step Instructions', List),
      'stepByStepInstructions',
      List
    );
  };

  const renderLikelyCauses = () => {
    const likelyCauses = 'likelyCauses' in diagnosisData ? diagnosisData.likelyCauses : undefined;
    if (!likelyCauses || likelyCauses.length === 0) return null;

    return renderExpandableSection(
      'Likely Causes',
      <View>
        {likelyCauses.map((cause, index) => (
          <Text key={index} style={styles.causeItem}>• {cause}</Text>
        ))}
      </View>,
      'likelyCauses',
      Lightbulb
    );
  };

  const renderPrognosis = () => {
    if (!diagnosisData.prognosis) return null;

    return (
      <Card style={styles.prognosisCard}>
        <View style={styles.prognosisHeader}>
          <TrendingUp size={20} color={Colors.primary} />
          <Text style={styles.prognosisTitle}>Prognosis</Text>
        </View>
        <Text style={styles.prognosisText}>{diagnosisData.prognosis}</Text>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      {/* Diagnosed Problem */}
      {renderDiagnosedProblem()}

      {/* Likely Causes */}
      {renderLikelyCauses()}

      {/* Immediate Actions */}
      {renderImmediateActions()}

      {/* Long-term Care */}
      {renderLongTermCare()}

      {/* Step-by-Step Instructions */}
      {renderStepByStepInstructions()}

      {/* Product Recommendations */}
      {renderProductRecommendations()}

      {/* Prevention Tips */}
      {renderPreventionTips()}

      {/* Prognosis */}
      {renderPrognosis()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  problemCard: {
    marginBottom: 16,
    backgroundColor: '#FFF8E1',
    borderLeftWidth: 4,
  },
  problemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  problemTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  problemTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 8,
  },
  severityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  severityText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.background,
  },
  problemDescription: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
    marginBottom: 12,
  },
  symptomsContainer: {
    marginTop: 8,
    padding: 12,
    backgroundColor: Colors.secondary,
    borderRadius: 8,
  },
  symptomsLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  symptomsText: {
    fontSize: 14,
    color: Colors.textMuted,
    lineHeight: 20,
  },
  sectionCard: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  sectionContent: {
    marginTop: 12,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
    gap: 12,
  },
  actionNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
  },
  actionNumberText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.background,
  },
  actionText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    flex: 1,
  },
  causeItem: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    marginBottom: 4,
  },
  prognosisCard: {
    marginBottom: 16,
    backgroundColor: Colors.primaryLight,
  },
  prognosisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  prognosisTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginLeft: 8,
  },
  prognosisText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
});
