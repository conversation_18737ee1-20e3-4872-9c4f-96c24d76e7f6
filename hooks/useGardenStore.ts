import { useState, useEffect } from 'react';
import createContextHook from '@nkzw/create-context-hook';
import { GardenPlant, Plant } from '@/types/plant';
import { DatabaseService, GardenCollection, PlantIdentification } from '@/services/database';
import { useAuth } from './useAuth';

// Helper function to map diagnosis severity to health status
const mapSeverityToHealthStatus = (severity?: string): 'healthy' | 'sick' | 'recovering' | 'critical' => {
  switch (severity?.toLowerCase()) {
    case 'mild':
    case 'moderate':
      return 'sick';
    case 'severe':
    case 'critical':
      return 'critical';
    default:
      return 'healthy';
  }
};

// Helper function to convert GardenCollection to GardenPlant
const convertToGardenPlant = (collection: GardenCollection): GardenPlant => {
  const plantData = collection.plant_identifications;
  const diagnosisData = collection.plant_diagnoses?.[0]; // Get the most recent diagnosis

  // Parse care instructions from JSON string if it's a string
  let careInstructions;
  try {
    if (typeof plantData?.care_instructions === 'string') {
      careInstructions = JSON.parse(plantData.care_instructions);
    } else {
      careInstructions = plantData?.care_instructions;
    }
  } catch (error) {
    console.error('Error parsing care instructions:', error);
    // Provide default care instructions if parsing fails
    careInstructions = {
      light: 'medium',
      water: 'medium',
      temperature: { min: 18, max: 25, unit: 'C' },
      humidity: 'medium',
      soil: 'Well-draining potting mix',
      fertilizer: 'Monthly during growing season',
      toxicity: 'none'
    };
  }

  return {
    id: collection.id,
    scientificName: plantData?.scientific_name || 'Unknown',
    commonName: plantData?.common_name || collection.nickname || 'Unknown Plant',
    imageUrl: plantData?.image_url || '',
    description: plantData?.description || '',
    careInstructions: careInstructions || {
      light: 'medium',
      water: 'medium',
      temperature: { min: 18, max: 25, unit: 'C' },
      humidity: 'medium',
      soil: 'Well-draining potting mix',
      fertilizer: 'Monthly during growing season',
      toxicity: 'none'
    },
    tags: plantData?.tags || [],
    addedDate: new Date(collection.created_at),
    nickname: collection.nickname,
    location: plantData?.location_taken, // Location where plant was identified
    locationInGarden: collection.location_in_garden, // Location within garden
    healthStatus: collection.health_status,
    lastWatered: collection.last_watered ? new Date(collection.last_watered) : undefined,
    lastFertilized: collection.last_fertilized ? new Date(collection.last_fertilized) : undefined,
    notes: collection.notes,
    isPublic: collection.is_public,
    // Add all the missing fields from plant_identifications table
    plantType: plantData?.plant_type,
    nativeRegion: plantData?.native_region,
    toxicityLevel: plantData?.toxicity_level,
    toxicityWarning: plantData?.toxicity_warning,
    growthHabit: plantData?.growth_habit,
    growthRate: plantData?.growth_rate,
    matureHeight: plantData?.mature_height,
    matureWidth: plantData?.mature_width,
    matureDescription: plantData?.mature_description,
    bloomTime: plantData?.bloom_time,
    flowerColors: plantData?.flower_colors,
    foliageType: plantData?.foliage_type,
    hardinessZones: plantData?.hardiness_zones,
    minTemperature: plantData?.min_temperature,
    pestsAndDiseases: plantData?.pests_and_diseases,
    funFacts: (() => {
      if (Array.isArray(plantData?.fun_facts)) {
        return plantData.fun_facts;
      } else if (typeof plantData?.fun_facts === 'string') {
        // If it's a string, try to split it by common delimiters
        return plantData.fun_facts.split(/[•\n]/).map(fact => fact.trim()).filter(fact => fact.length > 0);
      } else {
        return [];
      }
    })(),
    uses: plantData?.uses,
    propagation: plantData?.propagation,
    seasonalCare: plantData?.seasonal_care,
    companionPlants: plantData?.companion_plants,
    maintenanceLevel: plantData?.maintenance_level,
    diagnosis: diagnosisData ? {
      id: diagnosisData.id,
      diagnosedProblem: diagnosisData.diagnosed_problem,
      likelyCauses: diagnosisData.likely_causes,
      symptomsObserved: diagnosisData.symptoms_observed,
      severity: diagnosisData.severity,
      immediateActions: diagnosisData.immediate_actions,
      longTermCare: diagnosisData.long_term_care,
      productRecommendations: diagnosisData.product_recommendations,
      stepByStepInstructions: diagnosisData.step_by_step_instructions,
      preventionTips: diagnosisData.prevention_tips,
      prognosis: diagnosisData.prognosis,
      createdAt: new Date(diagnosisData.created_at),
    } : undefined,
  };
};

export const [GardenProvider, useGarden] = createContextHook(() => {
  const [plants, setPlants] = useState<GardenPlant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  // Load garden plants from database
  useEffect(() => {
    const loadGarden = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        const collections = await DatabaseService.getGardenCollections(user.id);
        const gardenPlants = collections.map(convertToGardenPlant);
        setPlants(gardenPlants);
      } catch (error) {
        console.error('Error loading garden:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadGarden();
  }, [user]);

  const addPlant = async (plant: Plant, nickname?: string, notes?: string, diagnosisId?: string, location?: string) => {
    if (!user) return;

    try {
      // Get diagnosis data if diagnosisId is provided to determine health status
      let healthStatus: 'healthy' | 'sick' | 'recovering' | 'critical' = 'healthy';
      if (diagnosisId) {
        try {
          const diagnosis = await DatabaseService.getPlantDiagnosis(diagnosisId);
          if (diagnosis) {
            healthStatus = mapSeverityToHealthStatus(diagnosis.severity);
          }
        } catch (error) {
          // Continue with default 'healthy' status
        }
      }

      // First create the plant identification
      const identification = await DatabaseService.createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: JSON.stringify(plant.careInstructions),
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: false,
        // Add all the new fields from the Plant type
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
      });

      if (!identification) {
        return;
      }

      // Then add to garden collection with appropriate health status
      const gardenItem = await DatabaseService.addToGarden({
        user_id: user.id,
        plant_identification_id: identification.id,
        nickname: nickname || plant.commonName,
        notes: notes || '',
        location_in_garden: location,
        health_status: healthStatus,
        is_public: false,
        allow_community_tips: true,
      });

      if (gardenItem) {
        // Show confirmation message
        console.log('Plant added to your garden successfully!');

        // If this plant was added from a diagnosis, link the garden collection to the diagnosis first
        if (diagnosisId) {
          try {
            await DatabaseService.updatePlantDiagnosis(diagnosisId, {
              garden_collection_id: gardenItem.id,
            });

            // Refresh the garden item from database to get the linked diagnosis data
            const updatedCollections = await DatabaseService.getGardenCollections(user.id);
            const updatedCollection = updatedCollections.find(c => c.id === gardenItem.id);

            if (updatedCollection) {
              const gardenPlant = convertToGardenPlant(updatedCollection);
              setPlants((current) => [gardenPlant, ...current]);
            } else {
              // Fallback to original item if refresh fails
              const gardenPlant = convertToGardenPlant(gardenItem);
              setPlants((current) => [gardenPlant, ...current]);
            }
          } catch (error) {
            // Still add the plant even if diagnosis linking fails
            const gardenPlant = convertToGardenPlant(gardenItem);
            setPlants((current) => [gardenPlant, ...current]);
          }
        } else {
          // No diagnosis to link, just add the plant normally
          const gardenPlant = convertToGardenPlant(gardenItem);
          setPlants((current) => [gardenPlant, ...current]);
        }
      }
    } catch (error) {
      // Error adding plant to garden
    }
  };

  const removePlant = async (plantId: string) => {
    try {
      const success = await DatabaseService.removeFromGarden(plantId);
      if (success) {
        setPlants((current) => current.filter((plant) => plant.id !== plantId));
        return true;
      } else {
        throw new Error('Failed to remove plant from database');
      }
    } catch (error) {
      throw error; // Re-throw to let the calling component handle the error
    }
  };

  const waterPlant = async (plantId: string) => {
    try {
      const updatedCollection = await DatabaseService.updateGardenCollection(plantId, {
        last_watered: new Date().toISOString(),
      });

      if (updatedCollection) {
        const updatedPlant = convertToGardenPlant(updatedCollection);
        setPlants((current) =>
          current.map((plant) =>
            plant.id === plantId ? updatedPlant : plant
          )
        );
      }
    } catch (error) {
      // Error updating plant watering
    }
  };

  const updatePlant = async (plantId: string, updates: Partial<GardenCollection>) => {
    try {
      const updatedCollection = await DatabaseService.updateGardenCollection(plantId, updates);

      if (updatedCollection) {
        const updatedPlant = convertToGardenPlant(updatedCollection);
        setPlants((current) =>
          current.map((plant) =>
            plant.id === plantId ? updatedPlant : plant
          )
        );
      }
    } catch (error) {
      // Error updating plant
    }
  };

  const shareGardenItem = async (plantId: string, isPublic: boolean, shareSettings: any) => {
    try {
      const updates: Partial<GardenCollection> = {
        is_public: isPublic,
        allow_community_tips: shareSettings.allowCommunityTips,
      };

      await updatePlant(plantId, updates);
    } catch (error) {
      throw error;
    }
  };

  const addPlantAndShare = async (plant: Plant, nickname?: string, notes?: string, diagnosisId?: string, location?: string) => {
    if (!user) return;

    try {
      // Get diagnosis data if diagnosisId is provided to determine health status
      let healthStatus: 'healthy' | 'sick' | 'recovering' | 'critical' = 'healthy';
      if (diagnosisId) {
        try {
          const diagnosis = await DatabaseService.getPlantDiagnosis(diagnosisId);
          if (diagnosis) {
            healthStatus = mapSeverityToHealthStatus(diagnosis.severity);
          }
        } catch (error) {
          // Continue with default 'healthy' status
        }
      }

      // First create the plant identification
      const identification = await DatabaseService.createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: JSON.stringify(plant.careInstructions),
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: true, // Make identification public for sharing
        // Add all the new fields from the Plant type
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
      });

      if (!identification) {
        return;
      }

      // Then add to garden collection with sharing enabled and appropriate health status
      const gardenItem = await DatabaseService.addToGardenAndShare({
        user_id: user.id,
        plant_identification_id: identification.id,
        nickname: nickname || plant.commonName,
        notes: notes || '',
        location_in_garden: location,
        health_status: healthStatus,
        is_public: true, // Make garden item public for sharing
        allow_community_tips: true,
      });

      if (gardenItem) {
        // Show confirmation message
        console.log('Plant added to your garden and shared with the community successfully!');

        // If this plant was added from a diagnosis, link the garden collection to the diagnosis first
        if (diagnosisId) {
          try {
            await DatabaseService.updatePlantDiagnosis(diagnosisId, {
              garden_collection_id: gardenItem.id,
              is_public: true, // Also make the diagnosis public
            });

            // Refresh the garden item from database to get the linked diagnosis data
            const updatedCollections = await DatabaseService.getGardenCollections(user.id);
            const updatedCollection = updatedCollections.find(c => c.id === gardenItem.id);

            if (updatedCollection) {
              const gardenPlant = convertToGardenPlant(updatedCollection);
              setPlants((current) => [gardenPlant, ...current]);
            } else {
              // Fallback to original item if refresh fails
              const gardenPlant = convertToGardenPlant(gardenItem);
              setPlants((current) => [gardenPlant, ...current]);
            }
          } catch (error) {
            // Still add the plant even if diagnosis linking fails
            const gardenPlant = convertToGardenPlant(gardenItem);
            setPlants((current) => [gardenPlant, ...current]);
          }
        } else {
          // No diagnosis to link, just add the plant normally
          const gardenPlant = convertToGardenPlant(gardenItem);
          setPlants((current) => [gardenPlant, ...current]);
        }
      }
    } catch (error) {
      // Error adding plant to garden and sharing
    }
  };

  const shareIdentificationOnly = async (plant: Plant) => {
    if (!user) return;

    try {
      await DatabaseService.shareIdentificationOnly(plant, plant.imageUrl, user.id);
      // Show confirmation message
      console.log('Plant identification shared with the community successfully!');
    } catch (error) {
      // Error sharing identification only
    }
  };

  const shareDiagnosisOnly = async (plant: Plant, diagnosisData: any) => {
    if (!user) return;

    try {
      await DatabaseService.shareDiagnosisOnly(plant, plant.imageUrl, user.id, diagnosisData);
      // Show confirmation message
      console.log('Plant diagnosis shared with the community successfully!');
    } catch (error) {
      // Error sharing diagnosis only
    }
  };

  return {
    plants,
    isLoading,
    addPlant,
    addPlantAndShare,
    shareIdentificationOnly,
    shareDiagnosisOnly,
    removePlant,
    waterPlant,
    updatePlant,
    shareGardenItem,
  };
});
